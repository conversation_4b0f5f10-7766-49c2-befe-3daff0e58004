import cv2
import numpy as np
from ultralytics import YOLO
import os

def process_video(input_video_path, output_video_path, model_path='yolov8n.pt'):
    """
    处理视频文件，检测并框选人类目标
    
    Args:
        input_video_path: 输入视频文件路径
        output_video_path: 输出视频文件路径
        model_path: YOLO模型文件路径
    """
    print("=== 视频人类检测系统 ===")
    
    # 检查输入视频文件是否存在
    if not os.path.exists(input_video_path):
        print(f"错误: 输入视频文件 '{input_video_path}' 不存在")
        return False
    
    # 加载YOLO模型
    print(f"正在加载YOLO模型: {model_path}")
    try:
        model = YOLO(model_path)
        print("模型加载完成")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return False
    
    # 打开输入视频
    print(f"正在打开输入视频: {input_video_path}")
    cap = cv2.VideoCapture(input_video_path)
    
    if not cap.isOpened():
        print("错误: 无法打开输入视频文件")
        return False
    
    # 获取视频属性
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息: {width}x{height}, {fps}fps, 总帧数: {total_frames}")
    
    # 设置输出视频编码器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
    
    if not out.isOpened():
        print("错误: 无法创建输出视频文件")
        cap.release()
        return False
    
    print(f"开始处理视频，输出文件: {output_video_path}")
    print("只检测和框选人类目标...")
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 显示处理进度
        if frame_count % 30 == 0:  # 每30帧显示一次进度
            progress = (frame_count / total_frames) * 100
            print(f"处理进度: {frame_count}/{total_frames} ({progress:.1f}%)")
        
        # YOLO检测
        results = model(frame, verbose=False)
        
        # 只处理人类检测结果（class 0 = person）
        if results[0].boxes is not None:
            boxes = results[0].boxes
            
            # 筛选出人类检测结果
            for box in boxes:
                # 获取类别ID（0表示人类）
                class_id = int(box.cls[0])
                
                if class_id == 0:  # 只处理人类
                    # 获取边界框坐标
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    
                    # 只显示置信度大于0.5的检测结果
                    if confidence > 0.5:
                        # 绘制边界框
                        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                        
                        # 添加标签
                        label = f"Person {confidence:.2f}"
                        cv2.putText(frame, label, (int(x1), int(y1) - 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # 添加说明文字
        cv2.putText(frame, "Human Detection Only", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # 写入输出视频
        out.write(frame)
    
    # 清理资源
    cap.release()
    out.release()
    
    print(f"视频处理完成！输出文件: {output_video_path}")
    print(f"总共处理了 {frame_count} 帧")
    return True

def main():
    """主函数"""
    # 可用的输入视频文件
    available_videos = []
    
    # 检查常见的视频文件
    video_files = ['行人视频素材.mp4']
    
    for video_file in video_files:
        if os.path.exists(video_file):
            available_videos.append(video_file)
    
    if not available_videos:
        print("未找到可用的视频文件")
        print("请确保以下文件之一存在:")
        for video_file in video_files:
            print(f"  - {video_file}")
        return
    
    print("找到以下可用的视频文件:")
    for i, video in enumerate(available_videos):
        print(f"  {i+1}. {video}")
    
   
    
    # 生成输出文件名
    base_name = os.path.splitext(input_video)[0]
    output_video = f"{base_name}_human_detection.mp4"
    
    # 处理视频
    success = process_video(input_video, output_video)
    
    if success:
        print(f"\n✅ 成功生成人类检测视频: {output_video}")
    else:
        print("\n❌ 视频处理失败")

if __name__ == "__main__":
    main()
