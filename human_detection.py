import cv2
import numpy as np
from ultralytics import YOL<PERSON>

def main():
    print("=== 人类检测系统 ===")

    # 加载YOLOv5模型
    print("正在加载YOLOv5模型...")
    model = YOLO('yolov8s.pt')
    print("模型加载完成")

    # 初始化摄像头
    print("正在初始化摄像头...")
    cap = cv2.VideoCapture(0)

    if not cap.isOpened():
        print("错误: 无法打开摄像头")
        return

    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    print("摄像头初始化完成")
    print("只检测人类目标，按 'q' 键退出程序")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # YOLO检测
        results = model(frame, verbose=False)

        # 只处理人类检测结果（class 0 = person）
        if results[0].boxes is not None:
            boxes = results[0].boxes

            # 筛选出人类检测结果
            for box in boxes:
                # 获取类别ID（0表示人类）
                class_id = int(box.cls[0])

                if class_id == 0:  # 只处理人类
                    # 获取边界框坐标
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()

                    # 只显示置信度大于0.5的检测结果
                    if confidence > 0.5:
                        # 绘制边界框
                        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

                        # 添加标签
                        label = f"Person {confidence:.2f}"
                        cv2.putText(frame, label, (int(x1), int(y1) - 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # 添加说明文字
        cv2.putText(frame, "Human Detection Only", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # 显示结果
        cv2.imshow('人类检测', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # 清理
    cap.release()
    cv2.destroyAllWindows()
    print("程序结束")

if __name__ == "__main__":
    main()
