# 人类检测系统

基于YOLOv5的实时人类检测系统，只检测和框出人类目标。

## 功能特点

- ✅ 只检测人类目标（忽略其他物体）
- ✅ 实时摄像头检测
- ✅ 简洁的界面显示
- ✅ 高置信度过滤（>0.5）

## 环境要求

- Python 3.7+
- OpenCV-Python
- ultralytics

## 安装依赖

```bash
# 在conda pytorch环境中
pip install ultralytics opencv-python
```

## 使用方法

```bash
python human_detection.py
```

- 程序会自动打开前置摄像头
- 只会检测和框出人类目标
- 绿色框表示检测到的人类
- 显示置信度分数
- 按 'q' 键退出程序

## 代码说明

程序使用YOLOv5模型进行目标检测，但只处理类别ID为0的检测结果（人类）：

```python
if class_id == 0:  # 只处理人类
    if confidence > 0.5:  # 置信度过滤
        # 绘制边界框和标签
```

## 文件结构

- `human_detection.py` - 主程序文件
- `yolov8s.pt` - YOLOv5模型权重文件
- `README.md` - 说明文档

## 注意事项

1. 首次运行会自动下载模型权重文件
2. 确保摄像头权限已开启
3. 建议在光线充足的环境下使用
4. 只检测人类，忽略其他所有目标
